import { createFileRoute, Outlet } from '@tanstack/react-router'
import { useState } from 'react'

import { Header } from '@/components/Header'
import { Sidebar } from '@/components/Sidebar'
import { AppProvider } from '@/contexts/app'
import { AuthProvider } from '@/contexts/auth'
import { getLoginURL, getToken } from '@/lib/auth'
import { cn } from '@/lib/utils'

export const Route = createFileRoute('/_auth')({
  component: RouteComponent,
  beforeLoad: () => {
    const token = getToken()
    if (!token) {
      window.location.href = getLoginURL()
    }
  },
  loader: () => {
    return {
      name: '首页',
    }
  },
})

function RouteComponent() {
  const [collapsed, setCollapsed] = useState(false)
  return (
    <AuthProvider>
      <AppProvider>
        <Header collapsed={collapsed} setCollapsed={setCollapsed} />
        <Sidebar collapsed={collapsed} />
        <main
          className={cn(
            'h-screen pt-[48px] pl-[240px] transition-[padding] duration-300 ease-[cubic-bezier(0.2,0,0,1)]',
            {
              'pl-[79px]': collapsed,
            },
          )}
        >
          <div className="h-full p-5">
            <Outlet />
          </div>
        </main>
      </AppProvider>
    </AuthProvider>
  )
}
