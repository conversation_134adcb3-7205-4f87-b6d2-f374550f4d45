export type EquityProjectDTO = {
  project_id?: string
  investment_year?: string
  company_id?: string
  company_name?: string
  investor_type?: number
  project_name?: string
  project_area?: string
  project_category?: string
  project_content?: string
  project_expect?: string
  industry_type?: string
  industry_new_type?: string
  equity_investment_type?: number
  is_plan_investment?: number
  project_plan_total_investment?: number
  investmentcompleted_last_year?: number
  plan_investment_cur_year?: number
  start_time?: string
  complete_time_expect?: string
  investment_return_rate_expect?: number
  special_consideration_type?: number
  special_total_investment?: number
  investment_necessity?: string
  fund_source_self?: number
  fund_source_loan?: number
  fund_source_other?: number
  remarks?: string
  version_id?: string
  project_progress_desc?: string
  investment_completed_cur_year?: number
  equity_ratio_after_investmen?: number
  completed_remarks?: string
  approval_id?: string
  approval_status?: number
  reject_reason?: string
  approval_node_id?: string
  approval_node_status?: number
  approval_update_at?: string
  creator_id?: string
  creator_name?: string
  modify_id?: string
  modify_name?: string
}

export interface FixedAssetDTO {
  actual_start_date: string
  approval_id: string
  approval_node_id: string
  approval_node_status: number
  approval_status: number
  approval_update_at: string
  company_id: string
  company_name: string
  complete_time_expect: string
  completed_remarks: string
  creator_id: string
  creator_name: string
  equipment_upgrade_amount: number
  fund_source_loan: number
  fund_source_other: number
  fund_source_self: number
  funds_received: number
  has_equipment_upgrade: number
  industry_new_type: string
  industry_type: string
  investment_completed_cur_year: number
  investment_necessity: string
  investment_return_rate_expect: number
  investment_year: string
  investmentcompleted_last_year: number
  investor_type: number
  is_key_project: number
  is_plan_investment: number
  is_real_estate_investment: number
  modify_id: string
  modify_name: string
  plan_investment_cur_year: number
  project_area: string
  project_category: string
  project_content: string
  project_expect: string
  project_id: string
  project_name: string
  project_phase: number
  project_plan_total_investment: number
  project_progress_desc: string
  reject_reason: string
  remarks: string
  special_consideration_type: number
  special_total_investment: number
  start_time: string
  version_id: string
}

export type PostEvaluationDTO = {
  approval_id: string
  approval_node_id: string
  approval_node_status: number
  approval_status: number
  approval_update_at: string
  company_id: string
  company_name: string
  complete_time_expect: string
  created_at: string
  creator_id: string
  creator_name: string
  eval_type: number
  industry_new_type: string
  industry_type: string
  investment_year: string
  is_major: number
  modify_id: string
  modify_name: string
  organization_type: number
  performance: string
  project_area: string
  project_category: string
  project_id: string
  project_name: string
  project_style: number
  project_total_investment: number
  region: number
  reject_reason: string
  start_time: string
  version_id: string
}
