import { useQuery } from '@tanstack/react-query'
import { Card, Col, DatePicker, Form, Input, message, Row, Select } from 'antd'
import { useState } from 'react'

import { useAuth } from '@/contexts/auth.tsx'
import { type APIResponse, request } from '@/lib/request.ts'
import type { MonthlyReportDTO } from '@/universal/data-summary/types.ts'

export const MonthlyReportForm = () => {
  const { user } = useAuth()

  const [form] = Form.useForm()

  const [existingData, setExistingData] = useState<MonthlyReportDTO>()

  const getExistingData = useQuery({
    queryKey: [user?.company_id],
    queryFn: async ({ queryKey: [company_id] }) => {
      const res = await request<APIResponse<MonthlyReportDTO>>(
        '/monthly-report/base-count',
        {
          query: { company_id: company_id },
        },
      )

      if (res.code !== 200001) {
        message.error(res.message)
        return null
      }

      setExistingData(res.data)
    },
    enabled: !!user?.company_id,
  })

  return (
    <Card title="投资月报-新建数据" loading={getExistingData.isLoading}>
      <Form form={form}>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item label="填报周期" name="period">
              <DatePicker
                picker="month"
                className="w-full"
                placeholder="请选择填报周期"
                format="YYYY-MM"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="编制单位" name="company_id">
              <Select
                placeholder="请选择编制单位"
                options={[{ label: user?.company, value: user?.company_id }]}
                onChange={(_, option) =>
                  form.setFieldValue('company_name', option?.label)
                }
              />
            </Form.Item>
            <Form.Item name="company_name" noStyle />
          </Col>
        </Row>

        <Row>
          <table className="border-collapse border border-[#EAEAEA] [&_td]:border [&_td]:border-[#EAEAEA] [&_td]:text-center [&_td]:text-nowrap">
            <thead>
              <tr className="bg-[#E5EBFE] text-nowrap [&>th]:p-2 [&>th]:font-normal">
                <th colSpan={2}>类型</th>
                <th>截至本月数据(万元)</th>
                <th>截至上月数据(万元)</th>
                <th>去年同期(万元)</th>
                <th>校验结果</th>
                <th>备注说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td colSpan={2}>合计</td>
                <td>
                  <Form.Item>
                    <Input disabled value="自动计算" />
                  </Form.Item>
                </td>
                <td>{existingData?.total_amount}</td>
                <td>1232</td>
                <td>
                  <span>较上月减少</span>
                </td>
                <td>
                  <Form.Item name="total_amount_remarks">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>

              <tr>
                <td rowSpan={4}>投资方式</td>
                <td>固定资产</td>
                <td>
                  <Form.Item>
                    <Input disabled value="自动计算" />
                  </Form.Item>
                </td>
                <td>{existingData?.fixed_assets_amount}</td>
                <td>固定资产</td>
                <td>固定资产</td>
                <td>
                  <Form.Item name="fixed_assets_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>其中:房地产</td>
                <td>
                  <Form.Item>
                    <Input disabled value="自动计算" />
                  </Form.Item>
                </td>
                <td>{existingData?.real_estate_amount}</td>
                <td>其中:房地产</td>
                <td>其中:房地产</td>
                <td>
                  <Form.Item name="real_estate_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>股权投资</td>
                <td>
                  <Form.Item>
                    <Input disabled value="自动计算" />
                  </Form.Item>
                </td>
                <td>{existingData?.equity_investment_amount}</td>
                <td>股权投资</td>
                <td>股权投资</td>
                <td>
                  <Form.Item name="equity_investment_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>其中:对外并购</td>
                <td>
                  <Form.Item>
                    <Input disabled value="自动计算" />
                  </Form.Item>
                </td>
                <td>{existingData?.foreign_equity_amount}</td>
                <td>其中:对外并购</td>
                <td>其中:对外并购</td>
                <td>
                  <Form.Item name="foreign_equity_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td rowSpan={2}>投资方向</td>
                <td>主业</td>
                <td>
                  <Form.Item>
                    <Input disabled value="自动计算" />
                  </Form.Item>
                </td>
                <td>{existingData?.main_business_amount}</td>
                <td>主业</td>
                <td>主业</td>
                <td>
                  <Form.Item name="main_business_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>非主业</td>
                <td>
                  <Form.Item>
                    <Input disabled value="自动计算" />
                  </Form.Item>
                </td>
                <td>{existingData?.non_main_business_amount}</td>
                <td>非主业</td>
                <td>非主业</td>
                <td>
                  <Form.Item name="non_main_business_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td rowSpan={2}>投资区域</td>
                <td>境内</td>
                <td>
                  <Form.Item>
                    <Input disabled value="自动计算" />
                  </Form.Item>
                </td>
                <td>{existingData?.domestic_amount}</td>
                <td>境内</td>
                <td>境内</td>
                <td>
                  <Form.Item name="domestic_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>境外</td>
                <td>
                  <Form.Item>
                    <Input disabled value="自动计算" />
                  </Form.Item>
                </td>
                <td>{existingData?.overseas_amount}</td>
                <td>境外</td>
                <td>境外</td>
                <td>
                  <Form.Item name="overseas_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td rowSpan={6}>重点领域</td>
                <td>战略性新兴产业</td>
                <td>
                  <Form.Item>
                    <Input disabled value="自动计算" />
                  </Form.Item>
                </td>
                <td>{existingData?.key_strategic_industry_amount}</td>
                <td>战略性新兴产业</td>
                <td>战略性新兴产业</td>
                <td>
                  <Form.Item name="key_strategic_industry_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>其中:固定资产投资</td>
                <td>
                  <Form.Item>
                    <Input disabled value="自动计算" />
                  </Form.Item>
                </td>
                <td>{existingData?.fixed_assets_amount}</td>
                <td>其中:固定资产投资</td>
                <td>其中:固定资产投资</td>
                <td>
                  <Form.Item name="fixed_assets_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>股权投资</td>
                <td>
                  <Form.Item>
                    <Input disabled value="自动计算" />
                  </Form.Item>
                </td>
                <td>{existingData?.key_equity_amount}</td>
                <td>股权投资</td>
                <td>股权投资</td>
                <td>
                  <Form.Item name="key_equity_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>对外并购</td>
                <td>
                  <Form.Item>
                    <Input disabled value="自动计算" />
                  </Form.Item>
                </td>
                <td>{existingData?.key_foreign_amount}</td>
                <td>对外并购</td>
                <td>对外并购</td>
                <td>
                  <Form.Item name="key_foreign_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>制造业</td>
                <td>
                  <Form.Item>
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
                <td>{existingData?.key_manufacture_amount}</td> <td>制造业</td>
                <td>制造业</td>
                <td>
                  <Form.Item name="key_manufacture_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>采矿业</td>
                <td>
                  <Form.Item>
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
                <td>{existingData?.key_mining_amount}</td>
                <td>采矿业</td>
                <td>采矿业</td>
                <td>
                  <Form.Item name="key_mining_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td colSpan={2} style={{ textAlign: 'left', textWrap: 'wrap' }}>
                  月度分析(简述行业趋势分析及其对投资的影响、投资主要方向及变化、重大项目关键节点进展，150字以内)
                </td>
                <td colSpan={5}>
                  <Form.Item name="monthly_analysis">
                    <Input.TextArea autoSize={{ minRows: 3, maxRows: 5 }} />
                  </Form.Item>
                </td>
              </tr>
            </tbody>
          </table>
        </Row>
      </Form>
    </Card>
  )
}
