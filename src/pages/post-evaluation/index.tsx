import { useQuery } from '@tanstack/react-query'
import { Link } from '@tanstack/react-router'
import {
  <PERSON><PERSON>,
  <PERSON>,
  DatePicker,
  Divider,
  Form,
  Input,
  Select,
  Table,
  type TableColumnsType,
  Tag,
  Typography,
} from 'antd'
import dayjs from 'dayjs'
import { CalendarClockIcon, FilterIcon } from 'lucide-react'
import numeral from 'numeral'
import { useState } from 'react'

import { FormItemPrefix } from '@/components/FormItemPrefix'
import { type APIResponse, request } from '@/lib/request.ts'
import {
  APPROVAL_STATUS,
  INDUSTRY_TYPE,
  PROJECT_CATEGORY,
} from '@/universal/basic-form/constants.ts'
import type { PostEvaluationDTO } from '@/universal/basic-form/types.ts'

const columns: TableColumnsType<PostEvaluationDTO> = [
  {
    title: '序号',
    align: 'center',
    render: (_, __, index) => {
      return index + 1
    },
    minWidth: 50,
  },
  {
    title: '编制单位',
    dataIndex: 'company_name',
    render: (value) => (
      <Typography.Text ellipsis={{ tooltip: value }}>{value}</Typography.Text>
    ),
    minWidth: 200,
  },
  {
    title: '项目名称',
    dataIndex: 'project_name',
    render: (value) => (
      <Typography.Text ellipsis={{ tooltip: value }}>{value}</Typography.Text>
    ),
    minWidth: 100,
  },
  {
    title: '国资/股权',
    dataIndex: 'project_style',
    minWidth: 100,
  },
  {
    title: '境内/境外',
    dataIndex: 'region',
    minWidth: 100,
  },
  {
    title: '省、自治区、直辖市或国家（地区）',
    dataIndex: 'project_area',
    minWidth: 250,
  },
  {
    title: '项目分类',
    dataIndex: 'project_category',
    minWidth: 100,
  },
  {
    title: '主业/非主业',
    dataIndex: 'is_major',
    minWidth: 100,
  },
  {
    title: '所属行业',
    dataIndex: 'industry_type',
    render: (value) => (
      <Typography.Text ellipsis={{ tooltip: value }}>{value}</Typography.Text>
    ),
    minWidth: 100,
  },
  {
    title: '所属战新产业',
    dataIndex: 'industry_new_type',
    render: (value) => (
      <Typography.Text ellipsis={{ tooltip: value }}>{value}</Typography.Text>
    ),
    minWidth: 100,
  },
  {
    title: '项目总投资（万元）',
    dataIndex: 'project_total_investment',
    align: 'right',
    render: (value) => (
      <Typography.Text ellipsis={{ tooltip: value }}>
        {numeral(value).format('0,0.00')}
      </Typography.Text>
    ),
    minWidth: 200,
  },
  {
    title: '项目开始时间',
    dataIndex: 'start_time',
    render: (text) => dayjs(text).format('YYYY-MM-DD'),
    minWidth: 100,
  },
  {
    title: '项目完成或预计完成时间',
    dataIndex: 'complete_time_expect',
    render: (text) => dayjs(text).format('YYYY-MM-DD'),
    minWidth: 200,
  },
  {
    title: '组织形式',
    dataIndex: 'organization_type',
    minWidth: 100,
  },
  {
    title: '评价方式',
    dataIndex: 'eval_type',
    minWidth: 100,
  },
  {
    title: '状态',
    dataIndex: 'approval_status',
    render: (value) => <Tag>{value}</Tag>,
    minWidth: 100,
  },
  {
    title: '最新上报月份',
    dataIndex: 'approval_update_at',
    render: (text) => dayjs(text).format('YYYY-MM'),
    minWidth: 100,
  },
  {
    title: '创建人',
    dataIndex: 'modify_name',
    minWidth: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    minWidth: 100,
  },
  {
    title: '操作',
    fixed: 'right',
    render: (_, record) => {
      return (
        <>
          <Link
            to="/basic-report/post-evaluation/$id/update"
            params={{ id: record.approval_node_id }}
          >
            <Button type="link" size="small">
              编辑
            </Button>
          </Link>
          <Divider type="vertical" />
          <Button type="link" size="small">
            修改记录
          </Button>
          <Divider type="vertical" />
          <Button type="link" danger size="small">
            删除
          </Button>
        </>
      )
    },
  },
]

export function PostEvaluationIndexPage() {
  const [form] = Form.useForm()

  const [filters, setFilters] = useState({})
  const [pagination, setPagination] = useState({ page_num: 1, page_size: 10 })

  const getTableData = useQuery({
    queryKey: [pagination, filters],
    queryFn: async ({ queryKey: [pagination, filters] }) => {
      const response = await request<
        APIResponse<{
          Data: PostEvaluationDTO[]
          Total: number
        }>
      >('/post-evaluation/list', {
        query: { use_total: true, ...pagination, ...filters },
      })
      if (response.code !== 200001) return null
      return response.data
    },
    staleTime: 0,
    retry: false,
  })

  return (
    <div className="flex h-full flex-col gap-4">
      <Card>
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">保利长大工程有限公司</h2>
          <div className="flex items-center gap-2">
            <CalendarClockIcon className="size-4" />
            <span className="text-sm text-[#666]">本年度项目计划总投资：</span>
            <span className="text-xl font-semibold">
              {numeral(73472163.213).format('0,0.00')}万元
            </span>
          </div>
        </div>
      </Card>
      <Card>
        <div className="flex flex-col gap-4">
          <Form
            form={form}
            onFinish={(values) => {
              const { date, ...data } = values
              const [start_time, end_time] = date

              setFilters({
                ...data,
                start_time: dayjs(start_time).format('YYYY-MM-DD HH:mm:ss'),
                end_time: dayjs(end_time).format('YYYY-MM-DD HH:mm:ss'),
              })
            }}
          >
            <div className="flex items-end gap-2">
              <div className="grid flex-1 grid-cols-3 gap-4 2xl:grid-cols-6">
                <Form.Item className="!mb-0" name="project_name">
                  <Input
                    className="w-full"
                    placeholder="请输入项目名称"
                    prefix={<FormItemPrefix title="项目名称" />}
                  />
                </Form.Item>
                <Form.Item className="!mb-0" name="project_category">
                  <Select
                    className="w-full"
                    placeholder="请选择项目分类"
                    prefix={<FormItemPrefix title="项目分类" />}
                    options={PROJECT_CATEGORY}
                    showSearch
                  />
                </Form.Item>
                <Form.Item className="!mb-0" name="industry_type">
                  <Select
                    className="w-full"
                    placeholder="请选择所属行业"
                    prefix={<FormItemPrefix title="所属行业" />}
                    options={INDUSTRY_TYPE}
                    showSearch
                    optionFilterProp="label"
                  />
                </Form.Item>
                <Form.Item className="!mb-0" name="approval_status">
                  <Select
                    className="w-full"
                    placeholder="请选择状态"
                    prefix={<FormItemPrefix title="状态" />}
                    options={APPROVAL_STATUS}
                  />
                </Form.Item>
                <Form.Item className="!mb-0" name="date">
                  <DatePicker.RangePicker
                    className="w-full"
                    prefix={<FormItemPrefix title="项目起始时间" />}
                  />
                </Form.Item>
              </div>
              <div className="flex shrink-0 grow-0 items-center gap-2">
                <Button
                  type="default"
                  icon={<FilterIcon className="size-3.5" />}
                />
                <Button type="primary" htmlType="submit">
                  搜索
                </Button>
                <Button type="text" htmlType="reset">
                  清空
                </Button>
              </div>
            </div>
          </Form>
          <div className="flex items-center justify-end gap-2">
            <Link to="/basic-report/post-evaluation/create">
              <Button type="primary">新建数据</Button>
            </Link>
            <Button>数据上报</Button>
            <Button>导入数据</Button>
            <Button>导出数据</Button>
            <Button danger>批量删除</Button>
          </div>
          <Table
            size="small"
            rowSelection={{
              type: 'checkbox',
              columnWidth: 40,
              align: 'center',
            }}
            dataSource={getTableData.data?.Data}
            columns={columns}
            scroll={{ x: 'max-content' }}
            sticky={{ offsetHeader: 48 }}
            pagination={{
              showQuickJumper: true,
              showSizeChanger: true,
              total: getTableData.data?.Total,
              onChange: (page, pageSize) => {
                setPagination({ page_num: page, page_size: pageSize })
              },
            }}
          />
        </div>
      </Card>
    </div>
  )
}
