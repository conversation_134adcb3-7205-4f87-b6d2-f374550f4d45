import { useQuery } from '@tanstack/react-query'
import { Link } from '@tanstack/react-router'
import {
  <PERSON><PERSON>,
  <PERSON>,
  DatePicker,
  Divider,
  Form,
  Input,
  Select,
  Table,
  type TableColumnsType,
  Tag,
  Typography,
} from 'antd'
import dayjs from 'dayjs'
import { CalendarClockIcon, FilterIcon } from 'lucide-react'
import numeral from 'numeral'
import { parseAsBoolean, parseAsInteger, useQueryStates } from 'nuqs'

import { FormItemPrefix } from '@/components/FormItemPrefix'
import { type APIResponse, request } from '@/lib/request.ts'
import type { EquityProjectDTO } from '@/universal/basic-form/types.ts'

export function EquityProjectsIndexPage() {
  const columns: TableColumnsType<EquityProjectDTO> = [
    {
      title: '序号',
      align: 'center',
      width: 60,
      render: (_, __, index) => {
        return index + 1
      },
    },
    {
      title: '编制单位',
      dataIndex: 'company_name',
      minWidth: 160,
      ellipsis: {
        showTitle: false,
      },
      render: (value) => (
        <Typography.Text ellipsis={{ tooltip: value }}>{value}</Typography.Text>
      ),
    },
    {
      title: '项目名称',
      dataIndex: 'project_name',
      minWidth: 160,
      ellipsis: {
        showTitle: false,
      },
      render: (value) => (
        <Typography.Text ellipsis={{ tooltip: value }}>{value}</Typography.Text>
      ),
    },
    {
      title: '投资分类',
      dataIndex: 'investor_type',
      width: 100,
    },
    {
      title: '项目地点',
      dataIndex: 'project_area',
      width: 100,
    },
    {
      title: '项目分类',
      dataIndex: 'project_category',
      width: 100,
    },
    {
      title: '所属行业',
      dataIndex: 'industry_type',
      minWidth: 100,
      ellipsis: {
        showTitle: false,
      },
      render: (value) => (
        <Typography.Text ellipsis={{ tooltip: value }}>{value}</Typography.Text>
      ),
    },
    {
      title: '所属战新产业',
      dataIndex: 'industry_new_type',
      minWidth: 120,
      ellipsis: {
        showTitle: false,
      },
      render: (value) => (
        <Typography.Text ellipsis={{ tooltip: value }}>{value}</Typography.Text>
      ),
    },
    {
      title: '项目计划总投资（万元）',
      dataIndex: 'project_plan_total_investment',
      minWidth: 180,
      align: 'right',
      ellipsis: {
        showTitle: false,
      },
      render: (value) => (
        <Typography.Text ellipsis={{ tooltip: value }}>
          {numeral(value).format('0,0.00')}
        </Typography.Text>
      ),
    },
    {
      title: '截至本年度完成投资（万元）',
      dataIndex: 'investment_completed_cur_year',
      minWidth: 200,
      align: 'right',
      ellipsis: {
        showTitle: false,
      },
      render: (value) => (
        <Typography.Text ellipsis={{ tooltip: value }}>
          {numeral(value).format('0,0.00')}
        </Typography.Text>
      ),
    },
    {
      title: '投资完成后所占股比',
      dataIndex: 'equity_ratio_after_investmen',
      width: 180,
      render: (text) => text + '%',
    },

    {
      title: '状态',
      dataIndex: 'approval_node_status',
      width: 120,
      render: (value) => {
        let color = 'cyan'
        if (value === '已更新待上报') {
          color = 'blue'
        }
        if (value === '已上报') color = 'green'
        if (value === '已驳回') color = 'red'
        if (value) {
          return <Tag color={color}>{value}</Tag>
        }
      },
    },
    {
      title: '最新上报月份',
      dataIndex: 'approval_update_at',
      width: 120,
      render: (text) => dayjs(text).format('YYYY-MM'),
    },
    {
      title: '最后修改人',
      dataIndex: 'modify_name',
      width: 120,
    },
    {
      title: '最后修改时间',
      dataIndex: 'approval_update_at',
      width: 160,
    },
    {
      title: '操作',
      width: 240,
      fixed: 'right',
      render: (_, record) => {
        return (
          <>
            <Link
              to="/basic-report/equity-projects/$id/update"
              params={{
                id: record?.approval_node_id as string,
              }}
            >
              <Button type="link" size="small">
                编辑
              </Button>
            </Link>
            <Divider type="vertical" />
            <Button type="link" size="small">
              修改记录
            </Button>
            <Divider type="vertical" />
            <Button type="link" danger size="small">
              删除
            </Button>
          </>
        )
      },
    },
  ]
  const [form] = Form.useForm()

  const [filters] = useQueryStates({
    page_num: parseAsInteger.withDefault(1),
    page_size: parseAsInteger.withDefault(10),
    use_total: parseAsBoolean.withDefault(false),
  })

  const getTableData = useQuery({
    queryKey: ['get', '/equity-investment/list', filters],
    queryFn: async ({ queryKey: [, url] }) => {
      const response = await request<
        APIResponse<{
          Data: EquityProjectDTO[]
          Total: number
        }>
      >(url as string)
      if (response.code !== 200001) return null
      return response.data
    },
    staleTime: 0,
    retry: false,
  })

  const onSearch = (values: unknown) => {
    console.log(values)
  }
  return (
    <div className="flex h-full flex-col gap-4">
      <Card>
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">保利长大工程有限公司</h2>
          <div className="flex items-center gap-2">
            <CalendarClockIcon className="size-4" />
            <span className="text-sm text-[#666]">本年度项目计划总投资：</span>
            <span className="text-xl font-semibold">
              {numeral(73472163.213).format('0,0.00')}万元
            </span>
          </div>
        </div>
      </Card>
      <Card>
        <div className="flex flex-col gap-4">
          <Form form={form} onFinish={onSearch}>
            <div className="flex items-end gap-2">
              <div className="grid flex-1 grid-cols-3 gap-4 2xl:grid-cols-6">
                <Form.Item
                  className="!mb-0"
                  name="name"
                  rules={[
                    { required: true, message: 'Please select your country!' },
                  ]}
                >
                  <Input
                    className="w-full"
                    placeholder="请输入"
                    prefix={<FormItemPrefix title="项目名称" />}
                  />
                </Form.Item>
                <Form.Item
                  className="!mb-0"
                  name="type"
                  rules={[
                    { required: true, message: 'Please select your country!' },
                  ]}
                >
                  <Select
                    className="w-full"
                    placeholder="请选择"
                    prefix={<FormItemPrefix title="投资分类" />}
                  />
                </Form.Item>
                <Form.Item
                  className="!mb-0"
                  name="type1"
                  rules={[
                    { required: true, message: 'Please select your country!' },
                  ]}
                >
                  <Select
                    className="w-full"
                    placeholder="请选择"
                    prefix={<FormItemPrefix title="项目分类" />}
                  />
                </Form.Item>
                <Form.Item
                  className="!mb-0"
                  name="hangye"
                  rules={[
                    { required: true, message: 'Please select your country!' },
                  ]}
                >
                  <Select
                    className="w-full"
                    placeholder="请选择"
                    prefix={<FormItemPrefix title="所属行业" />}
                  />
                </Form.Item>
                <Form.Item
                  className="!mb-0"
                  name="status"
                  rules={[
                    { required: true, message: 'Please select your country!' },
                  ]}
                >
                  <Select
                    className="w-full"
                    placeholder="请选择"
                    prefix={<FormItemPrefix title="状态" />}
                  />
                </Form.Item>
                <Form.Item
                  className="!mb-0"
                  name="date"
                  rules={[
                    { required: true, message: 'Please select your country!' },
                  ]}
                >
                  <DatePicker.RangePicker
                    className="w-full"
                    prefix={<FormItemPrefix title="创建时间" />}
                  />
                </Form.Item>
              </div>
              <div className="flex shrink-0 grow-0 items-center gap-2">
                <Button
                  type="default"
                  icon={<FilterIcon className="size-3.5" />}
                />
                <Button type="primary" htmlType="submit">
                  搜索
                </Button>
                <Button type="text" htmlType="reset">
                  清空
                </Button>
              </div>
            </div>
          </Form>
          <div className="flex items-center justify-end gap-2">
            <Link to="/basic-report/equity-projects/create">
              <Button type="primary">新建数据</Button>
            </Link>
            <Button>新建上报</Button>
            <Button>导入数据</Button>
            <Button>导出数据</Button>
            <Button danger>批量删除</Button>
          </div>
          <Table
            size="small"
            rowSelection={{
              type: 'checkbox',
              columnWidth: 40,
              align: 'center',
            }}
            dataSource={getTableData.data?.Data}
            columns={columns}
            scroll={{ x: 'max-content' }}
            sticky={{ offsetHeader: 48 }}
            pagination={{
              showQuickJumper: true,
              showSizeChanger: true,
              total: 100,
            }}
            rowKey="approval_node_id"
          />
        </div>
      </Card>
    </div>
  )
}
